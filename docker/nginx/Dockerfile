FROM nginx:stable-alpine

# Crear directorios necesarios para archivos temporales
RUN mkdir -p /tmp/nginx-client-body && \
    chown -R nginx:nginx /tmp/nginx-client-body && \
    chmod 755 /tmp/nginx-client-body

# Remover configuración por defecto
RUN rm /etc/nginx/conf.d/default.conf

# Copiar configuraciones personalizadas
COPY nginx-main.conf /etc/nginx/nginx.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
