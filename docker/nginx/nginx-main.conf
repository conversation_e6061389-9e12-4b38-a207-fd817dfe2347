user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Configuración de logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Configuración básica
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    types_hash_max_size 2048;
    server_names_hash_bucket_size 64;

    # Configuración de seguridad
    server_tokens off;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Configuración para archivos grandes y Livewire
    client_max_body_size 2048M;
    client_body_timeout 3600s;
    client_header_timeout 300s;
    send_timeout 3600s;
    client_body_buffer_size 1M;
    client_header_buffer_size 32k;
    large_client_header_buffers 8 128k;
    
    # Configuración adicional para evitar 403
    client_body_in_file_only clean;
    client_body_temp_path /tmp/nginx-client-body;
    
    # Timeouts
    keepalive_timeout 300s;
    keepalive_requests 10000;
    
    # Configuración de proxy
    proxy_buffer_size 256k;
    proxy_buffers 8 512k;
    proxy_busy_buffers_size 1024k;
    proxy_temp_file_write_size 1024k;
    proxy_max_temp_file_size 2048m;
    proxy_connect_timeout 300s;
    proxy_send_timeout 3600s;
    proxy_read_timeout 3600s;

    # Configuración de FastCGI
    fastcgi_buffer_size 256k;
    fastcgi_buffers 128 64k;
    fastcgi_busy_buffers_size 512k;
    fastcgi_temp_file_write_size 512k;
    fastcgi_max_temp_file_size 2048m;
    fastcgi_connect_timeout 300s;
    fastcgi_send_timeout 3600s;
    fastcgi_read_timeout 3600s;

    # Configuración de Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # Incluir configuraciones del servidor
    include /etc/nginx/conf.d/*.conf;
}
