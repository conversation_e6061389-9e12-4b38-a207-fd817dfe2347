server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    server_tokens off;

    autoindex off;
    etag off;

    if ($server_protocol ~* "HTTP/1.0") {
        return 444;
    }
    ssi off;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Frame-Options "SAMEORIGIN" always;

    proxy_cookie_path / "/; HttpOnly; Secure";
    add_header Set-Cookie "Path=/; HttpOnly; Secure";

    if ($request_method = TRACE) {
        return 405;
    }

    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$) {
        return 405;
    }

    index index.php index.html index.htm;
    charset utf-8;

    # Configuración optimizada para archivos base64 grandes y Livewire uploads
    client_max_body_size 2048M;
    client_body_timeout 3600s;
    client_header_timeout 300s;
    send_timeout 3600s;
    client_body_buffer_size 1M;
    client_header_buffer_size 32k;
    large_client_header_buffers 8 128k;

    # Configuración adicional para evitar 403 con requests grandes
    client_body_in_file_only clean;
    client_body_temp_path /tmp/nginx-client-body;

    # Timeouts más generosos
    keepalive_timeout 300s;
    keepalive_requests 10000;

    # Configuración de proxy para requests grandes
    proxy_buffer_size 256k;
    proxy_buffers 8 512k;
    proxy_busy_buffers_size 1024k;
    proxy_temp_file_write_size 1024k;
    proxy_max_temp_file_size 2048m;

    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # Configuración específica para Livewire uploads
    location /livewire {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 2048M;
        client_body_timeout 3600s;
        send_timeout 3600s;
        client_header_buffer_size 32k;
        large_client_header_buffers 8 128k;

        # Headers específicos para Livewire
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
    }

    # Configuración específica para Filament/Admin
    location /admin {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 2048M;
        client_body_timeout 3600s;
        send_timeout 3600s;
        client_header_buffer_size 32k;
        large_client_header_buffers 8 128k;
    }

    location /qr/ {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 2048M;
        client_body_timeout 3600s;
        gzip on;
        gzip_types text/plain application/json;
    }

    location /upload/ {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 2048M;
        client_body_timeout 3600s;
        send_timeout 3600s;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico {
        access_log off;
        log_not_found off;
        try_files $uri =204;
    }

    location = /robots.txt {
        access_log off;
        log_not_found off;
        try_files $uri =204;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|zip|rar|doc|docx)$ {
        try_files $uri =404;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        etag off;
        if_modified_since off;
        add_header Last-Modified "";
        client_max_body_size 2048M;
    }

    error_page 404 /index.php;

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;

        # Timeouts aumentados para Livewire y archivos grandes
        fastcgi_read_timeout 3600;
        fastcgi_send_timeout 3600;
        fastcgi_connect_timeout 300;

        # Buffers aumentados para FastCGI
        fastcgi_buffers 128 64k;
        fastcgi_buffer_size 256k;
        fastcgi_busy_buffers_size 512k;
        fastcgi_temp_file_write_size 512k;

        # Configuración adicional para requests grandes
        fastcgi_request_buffering on;
        fastcgi_max_temp_file_size 2048m;

        fastcgi_hide_header X-Powered-By;
        fastcgi_param PHP_VALUE "
            upload_max_filesize=2048M
            post_max_size=2048M
            memory_limit=2048M
            max_execution_time=3600
            max_input_time=3600
            max_input_vars=50000
            max_file_uploads=200
            expose_php=Off
            file_uploads=On";
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~ \.(htaccess|htpasswd|conf|ini|txt|log)$ {
        deny all;
    }
}
