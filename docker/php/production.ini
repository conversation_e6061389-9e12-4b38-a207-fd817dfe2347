[PHP]
; Configuración optimizada para producción con archivos grandes y Livewire

; Configuración de uploads
upload_max_filesize = 2048M
post_max_size = 2048M
max_file_uploads = 200

; Configuración de memoria y tiempo
memory_limit = 2048M
max_execution_time = 3600
max_input_time = 3600
max_input_vars = 50000
default_socket_timeout = 120

; Configuración de archivos
file_uploads = On
auto_detect_line_endings = Off

; Configuración de sesiones
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Strict"

; Configuración de errores para producción
display_errors = Off
display_startup_errors = Off
log_errors = On
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
log_errors_max_len = 1024
ignore_repeated_errors = On
ignore_repeated_source = Off
html_errors = Off

; Configuración de OPcache
opcache.enable = 1
opcache.enable_cli = 0
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 32
opcache.max_accelerated_files = 20000
opcache.validate_timestamps = 0
opcache.revalidate_freq = 0
opcache.save_comments = 0
opcache.fast_shutdown = 1
opcache.enable_file_override = 1
opcache.optimization_level = 0x7FFFBFFF
opcache.inherited_hack = 1
opcache.dups_fix = 1
opcache.blacklist_filename = ""

; Configuración de realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Configuración de output buffering
output_buffering = 4096
implicit_flush = Off

; Configuración de recursos
max_input_nesting_level = 64
max_input_vars = 50000
user_ini.filename = ""
user_ini.cache_ttl = 300

; Configuración de seguridad
expose_php = Off
allow_url_fopen = On
allow_url_include = Off
enable_dl = Off

; Configuración de mail
sendmail_path = /usr/sbin/sendmail -t -i

; Configuración de fecha
date.timezone = "UTC"

; Configuración de MySQLi
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.default_port = 3306
mysqli.default_socket = ""
mysqli.default_host = ""
mysqli.default_user = ""
mysqli.default_pw = ""
mysqli.reconnect = Off

; Configuración de PostgreSQL
pgsql.allow_persistent = On
pgsql.auto_reset_persistent = Off
pgsql.max_persistent = -1
pgsql.max_links = -1
pgsql.ignore_notice = 0
pgsql.log_notice = 0

; Configuración de cURL
curl.cainfo = ""

; Configuración de OpenSSL
openssl.cafile = ""
openssl.capath = ""
