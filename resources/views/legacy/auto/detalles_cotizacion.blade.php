<h5 style="text-align: center; background-color: #007bff; color: #fff; padding: 8px;">
    COBERTURAS
</h5>

<table width="100%" cellpadding="4" cellspacing="0" border="1" style="border-collapse: collapse; font-size: 12px;">
    {{-- Cabecera: primera columna vacía + una columna por cada aseguradora --}}
    <thead>
    <tr>
        <th style="background-color: #f0f0f0;"></th>
        @foreach ($cotizacion->getLineItems() as $lineItem)
            @if ($lineItem->getNetTotal() > 0)
                @php
                    $plan = $libreria->getRecord("Products", $lineItem->getProduct()->getEntityId());
                    $logo = public_path('img/aseguradoras/' . $plan->getFieldValue('Vendor_Name')->getLookupLabel() . '.png');
                @endphp
                <th style="text-align: center;">
                    <img src="{{ $logo }}" height="50"
                         alt="{{ $plan->getFieldValue('Vendor_Name')->getLookupLabel() }}">
                </th>
            @endif
        @endforeach
    </tr>
    </thead>

    <tbody>
    {{-- Sección: Daños propios --}}
    @php
        $riesgo_compresivo = $cotizacion->getFieldValue('Suma_asegurada') * ($plan->getFieldValue('Riesgos_comprensivos') / 100);
        $colision = $cotizacion->getFieldValue('Suma_asegurada') * ($plan->getFieldValue('Colisi_n_y_vuelco') / 100);
        $incendio = $cotizacion->getFieldValue('Suma_asegurada') * ($plan->getFieldValue('Incendio_y_robo') / 100);
    @endphp

    @foreach ([
        'DAÑOS PROPIOS' => [
            'Riesgos Comprensivos'                  => fn($p) => 'RD$'. number_format($cotizacion->getFieldValue('Suma_asegurada') * ($p->getFieldValue('Riesgos_comprensivos')/100)),
            'Riesgos Compr. (Deducible)'            => fn($p) => $p->getFieldValue('Riesgos_comprensivos_deducible'),
            'Rotura de Cristales (Deducible)'      => fn($p) => $p->getFieldValue('Rotura_de_cristales_deducible'),
            'Colisión y Vuelco'                     => fn($p) => 'RD$'. number_format($cotizacion->getFieldValue('Suma_asegurada') * ($p->getFieldValue('Colisi_n_y_vuelco')/100)),
            'Incendio y Robo'                       => fn($p) => 'RD$'. number_format($cotizacion->getFieldValue('Suma_asegurada') * ($p->getFieldValue('Incendio_y_robo')/100)),
        ],
        'RESPONSABILIDAD CIVIL' => [
            'Daños Propiedad Ajena'                 => fn($p) => 'RD$'. number_format($p->getFieldValue('Da_os_propiedad_ajena')),
            'Lesiones/Muerte 1 Pers'                => fn($p) => 'RD$'. number_format($p->getFieldValue('Lesiones_muerte_1_pers')),
            'Lesiones/Muerte más de 1 Pers'         => fn($p) => 'RD$'. number_format($p->getFieldValue('Lesiones_muerte_m_s_1_pers')),
            'Lesiones/Muerte 1 Pasajero'            => fn($p) => 'RD$'. number_format($p->getFieldValue('Lesiones_muerte_1_pas')),
            'Lesiones/Muerte más de 1 Pas'          => fn($p) => 'RD$'. number_format($p->getFieldValue('Lesiones_muerte_m_s_1_pas')),
        ],
        'RIESGOS CONDUCTOR'  => [
            'Riesgos Conductor'                     => fn($p) => 'RD$'. number_format($p->getFieldValue('Riesgos_conductor')),
            'Fianza Judicial'                       => fn($p) => 'RD$'. number_format($p->getFieldValue('Fianza_judicial')),
        ],
        'COBERTURAS ADICIONALES' => [
            'Asistencia Vial'                       => function($p) use ($cotizacion) {
                if (!$p->getFieldValue('Asistencia_vial')) return 'No aplica';
                if (preg_match('/\bpesado\b/i', $cotizacion->getFieldValue("Tipo_veh_culo")) ||
                    $cotizacion->getFieldValue("Tipo_veh_culo") == "Camión") {
                    return 'No aplica';
                }
                return $p->getFieldValue('Valor_asistencia_vial')
                    ? 'Aplica (RD$'.number_format($p->getFieldValue('Valor_asistencia_vial')).')'
                    : 'Aplica';
            },
            'Renta Vehículo'                        => function($p) use ($cotizacion) {
                if (!$p->getFieldValue('Renta_veh_culo')) return 'No aplica';
                if (preg_match('/\bpesado\b/i', $cotizacion->getFieldValue("Tipo_veh_culo")) ||
                    $cotizacion->getFieldValue("Tipo_veh_culo") == "Camión") {
                    return 'No aplica';
                }
                return 'Aplica';
            },
            'En Caso de Accidente'                  => fn($p) => $p->getFieldValue('En_caso_de_accidente') ?: 'No aplica',
        ],
    ] as $section => $items)
        {{-- Título de sección --}}
        <tr>
            <td colspan="{{ 1 + count($cotizacion->getLineItems()->filter(fn($li)=>$li->getNetTotal()>0)) }}"
                style="background-color: #f8f8f8; font-weight: bold;">
                {{ $section }}
            </td>
        </tr>
        {{-- Filas de cada cobertura --}}
        @foreach ($items as $label => $callback)
            <tr>
                <td>{{ $label }}</td>
                @foreach ($cotizacion->getLineItems() as $lineItem)
                    @if ($lineItem->getNetTotal() > 0)
                        @php $plan = $libreria->getRecord("Products", $lineItem->getProduct()->getEntityId()); @endphp
                        <td style="text-align: center;">
                            {{ $callback($plan) }}
                        </td>
                    @endif
                @endforeach
            </tr>
        @endforeach
    @endforeach

    {{-- Totales --}}
    <tr style="font-weight: bold;">
        <td>PRIMA NETA {{ $cotizacion->getFieldValue("Plan") == "Mensual Full" ? "MENSUAL" : "ANUAL" }}</td>
        @foreach ($cotizacion->getLineItems() as $li)
            @if ($li->getNetTotal() > 0)
                <td style="text-align: center;">
                    RD$ {{ number_format($li->getNetTotal() / 1.16, 2) }}
                </td>
            @endif
        @endforeach
    </tr>
    <tr style="font-weight: bold;">
        <td>ISC</td>
        @foreach ($cotizacion->getLineItems() as $li)
            @if ($li->getNetTotal() > 0)
                <td style="text-align: center;">
                    RD$ {{ number_format($li->getNetTotal() - $li->getNetTotal() / 1.16, 2) }}
                </td>
            @endif
        @endforeach
    </tr>
    <tr style="font-weight: bold;">
        <td>PRIMA TOTAL {{ $cotizacion->getFieldValue("Plan") == "Mensual Full" ? "MENSUAL" : "ANUAL" }}</td>
        @foreach ($cotizacion->getLineItems() as $li)
            @if ($li->getNetTotal() > 0)
                <td style="text-align: center;">
                    RD$ {{ number_format($li->getNetTotal(), 2) }}
                </td>
            @endif
        @endforeach
    </tr>
    </tbody>
</table>
